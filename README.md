<div id='top'/>

# PATIO<br/>

---

# Serenity BDD Test Automation Project

---

<!--TABLE OF CONTENTS-->

# Table of Contents
### 1. [Requirements](#requirements)
### 2. [Code location](#code-location)
### 3. [Configurations](#configurations)
* [JAVA](#setup-java)
* [Maven](#setup-maven)
* [IntelliJ IDE](#intellij-ide)
* [serenity.conf](#serenity-conf)
* [Database](#database)
### 4. [Running tests](#running-tests)
* [RunnerTestSuite class](#runner-class)
* [Command line](#command-line)
* [Parallel run](#parallel-run)
### 5. [Project dependencies](#project-dependencies)
### 6. [Project structure](#project-structure)
### 7. [Usage](#usage)
### 8. [Report](#report)
### 9. [Internal conventions](#internal-conventions)
### 10. [Tips](#tips)
### 11. [Additional Documentations](#additional-documentations)

---

<!--REQUIREMENTS-->

<div id='requirements'/>

## 1.Requirements

- [IntelliJ IDEA](https://www.jetbrains.com/idea/)
- [Java Development Kit 21 (JDK21)](https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html)
- [Maven 3](https://maven.apache.org/download.cgi#)

---

<!--CODE LOCATION-->

<div id='code-location'/>

## 2. Code location

Azure Devops Repos:

```
https://github.com/Dajar-Internet/patio_ui_automation_test
```
---

<!--CONFIGURATIONS-->


<!--CONFIGURATIONS-->

<div id='configurations'/>

## 3. Configurations

<div id='setup-java'/>

### - Java
[Setup Java](https://phoenixnap.com/kb/install-java-ubuntu) – setup on **Linux/Ubuntu**

#### Install Java (example for OpenJDK 21)
```bash
sudo apt update
sudo apt install openjdk-21-jdk
```

#### Set environment variables
Add to your `~/.bashrc` or `~/.zshrc`:
```bash
export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
```
Then run:
```bash
source ~/.bashrc
```
Verify:
```bash
java -version
```

<div id='setup-maven'/>

### - Maven
[Setup Maven](https://phoenixnap.com/kb/install-maven-ubuntu) – setup on **Linux/Ubuntu**

#### Install Maven
```bash
sudo apt update
sudo apt install maven
```

#### Set environment variables (optional if installed via `apt`)
If using manual installation (e.g. from Apache):
```bash
export MAVEN_HOME=/opt/maven
export PATH=$MAVEN_HOME/bin:$PATH
```
Then run:
```bash
source ~/.bashrc
```
Verify:
```bash
mvn -version
```

<div id='intellij-ide'/>

### - IntelliJ IDE

#### Install IntelliJ IDEA Community
You can download directly from JetBrains: [Download IntelliJ IDEA Community](https://www.jetbrains.com/idea/download/#section=linux)

Or install via Snap:
```bash
sudo snap install intellij-idea-community --classic
```

#### -- How to check if dependencies failed to download:
1. In IntelliJ, on the right side, open the **Maven** tab and find **Dependencies**. If all or some are underlined in red, your code is not running.
2. Or, if you open any class and many lines require imports but IntelliJ cannot resolve them automatically, your code is not running.
3. However, if you can run the code even though dependencies are underlined in red, it could be an IntelliJ cache issue.

#### -- How to fix dependency issues:
1. Open **File → Invalidate Caches...**, check:
    - `Clear file system cache and Local History`
    - `Clear VCS Log caches and indexes`

   Click **Invalidate and Restart**. Wait for all dependencies to download (ensure VPN is OFF).

2. Delete your local Maven repository:
```bash
rm -rf ~/.m2/repository
```
Then reopen IntelliJ — it will automatically start downloading dependencies. Wait until it's completed

#### -- Setup IntelliJ first time:
1. Open **File → Project Structure** → check the **Project**, **Modules**, and **SDKs** tabs — ensure Java 21 is selected everywhere.
2. Open **File → Settings → Plugins**: install **Cucumber for Java** (plugin improves feature file syntax highlighting and support).


<div id='serenity-conf'/>

### - serenity.conf
[Serenity conf file documentation - 1](https://github.com/serenity-bdd/the-serenity-book/blob/master/modules/ROOT/pages/web-testing-in-serenity.adoc)

[Serenity conf file documentation - 2](https://github.com/serenity-bdd/the-serenity-book/blob/master/modules/ROOT/pages/serenity-system-properties.adoc)

<!--RUNNING TESTS-->

<div id='running-tests'/>

## 4. Running tests

<div id='runner-class'/>

### - RunnerTestSuite class

Add tag you want to run to 'tags' and execute RunnerTestSuite class by selecting 'Run RunnerTestSuite'

<div id='command-line'/>

### - Command line

```
$ mvn clean verify - execute tag from RunnerTestSuite.class
$ mvn clean verify -Dgroups='putTagHere' - execute by tag (Note that the @ symbol is not part of the JUnit5 tag)
$ mvn clean verify -Dgroups='tag1 | tag2' - seperate tags by delimiter character ("|" stands for "or" and "&" stands for "and")
```

Override the driver specified in the configuration file from the command line.

```
$ mvn clean verify -Dwebdriver.driver=nameOfBrowser
```

Override the environment specified in the configuration file from the command line.

```
$ mvn clean verify -Denvironment=nameOfEnvironment
```

<div id='parallel-run'/>

### - Parallel run
 
Parallel run is executed from command line.
Setup located in junit-platform.properties file.

```
cucumber.execution.parallel.enabled=true - enable parallel execution
cucumber.execution.parallel.config.strategy=fixed
cucumber.execution.parallel.config.fixed.parallelism=12 - enter number of threads (depends from the project)
cucumber.execution.parallel.config.fixed.max-pool-size=12 - enter number of threads (depends from the project)
```

---

<!--PROJECT DEPENDENCIES-->

<div id='project-dependencies'/>

## 5. Project dependencies

* `serenity-rest-assured` -> all Rest Assured tests are valid tests for Serenity BDD.
* `serenity-cucumber` -> run Serenity tests and generate Serenity reports using Cucumber 6.
* `serenity-junit` -> provide annotations to tag tests and test classes.
* `javafaker` -> libraries with different fake data for testing.
* `awaitility` -> a Java DSL for synchronizing asynchronous operations.
---

<!--PROJECT STRUCTURE-->

<div id='project-structure'/>

## 6. Project structure

* `src`
    * `main`
        * `java`
            * `actions` - all actions store here.
            * `common`:
                * `constants` - constant values that are separated for better readability and maintainability of code.
            * `objects_behaviors` - abstract factory design pattern for Selenium(like Utility)
            * `pages` - plain Page Objects for all web pages.
            * `utils` - data related utility classes.
    * `test`
        * `java`
            * `runner` - core runner class.
            * `steps` - step definition classes.
        * `resources` - contains serenity configurations
            * `features` - all test feature files on Gherkin language.
---

<!--USAGE-->

<div id='usage'/>

<!--REPORT-->

<div id='report'/>

## 7. Report

**Location:** Target -> site -> serenity:
* **SINGLE PAGE REPORT:** -> serenity-summary.html
* **FULL REPORT:** -> index.html

**Generate report:**
* It is always generated when execute tests from command line, for example: ***mvn clean verify***
* If execute test through RunnerTestSuite class, then to generate report use command: ***mvn serenity:aggregate***

---
<!--INTERNAL CONVENTIONS-->

<div id='internal-conventions'/>

## 9. Internal conventions

* **Feature file:** <br>
  -**Tags:** starts from '@' and all lowercase.<br>
  -**Generate Step Def files:** always generate step def method through RunnerTestSuite class, <br>
  -by changing dryRun to true and passing correct tag.


* **Step Def:**<br>
  -Method: naming of the method should follow underscore convention 'method_name'.


* **Packages:**<br>
  -Should be lowercase and follow underscore convention.<br>
  -Naming of the packages and classes should be the same in Pages, Actions, Step Def, <br>
  -just add package name at the end.<br>
  -**For example:** Test Actions, TestPages, TestSteps.
---

<!--TIPS-->

<div id='tips'/>

## 10. Tips

* Start Automation in next steps: Feature file -> Step Def -> Actions -> Pages
* Use command 'CTRL + ALT + L' - it will fix style on the page
* Before commit changes on git do next: Click the right button on Project and select 'Optimize Imports' (automatically delete all unused imports in project)

---

<!--ADDITIONAL DOCUMENTATIONS-->

<div id='additional-documentations'/>

## 11. Additional Documentations

- [Serenity BDD Framework](https://serenity-bdd.github.io/docs/guide/user_guide_intro)
- [Lombok](https://projectlombok.org/features/all)
- [AssertJ](https://assertj.github.io/doc/)
- [Cucumber](https://cucumber.io/)
- [Cucumber BDD DOC Gherkin](https://cucumber.io/docs/gherkin/reference/)
- [BDD Gherkin rules](https://techbeacon.com/app-dev-testing/better-behavior-driven-development-4-rules-writing-good-gherkin)
---


## [GO TOP](#top)