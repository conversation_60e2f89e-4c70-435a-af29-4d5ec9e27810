@regression  @logged_in_order
Feature: Logged In User Order Tests - 1

  Background: Open the home page and log in with first account
    Given that user is on the home page
    And user logs in with order logged in credentials
    And remove product from cart if present

  @logged_in_order @0050 @order_logged_in_sequential_1 @smoke
  Scenario: Verify successful order placement with single product as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0051 @order_logged_in_sequential_1 @smoke
  Scenario: Verify order placement with single product as a logged in user with company profile
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0052 @order_logged_in_sequential_1
  Scenario: Verify successful order placement with multiple products as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user returns to the previous page
    And user opens the second product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Online transfer"
    Then verify order confirmation page is displayed

  @logged_in_order @0053 @order_logged_in_sequential_1 @smoke
  Scenario: Verify order placement with discount code as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And user applies discount code "automation"
    And open cart and proceed to checkout
    Then verify discount is applied correctly and total price is updated with discount applied
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Card"
    Then verify order confirmation page is displayed

  @logged_in_order @0054 @order_logged_in_sequential_1 @smoke
  Scenario Outline: Verify order placement with different payment methods as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "<paymentMethod>"
    Then verify order confirmation page is displayed
    Examples:
      | paymentMethod   |
      | Blik            |
      | Card            |
      | Online transfer |

  @logged_in_order @0055 @order_logged_in_sequential_1 @smoke
  Scenario Outline: Verify order placement with different delivery methods as a logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And click on continue button
    And user selects delivery method "<deliveryMethod>"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    Examples:
      | deliveryMethod   |
      | Kurier           |
      | Odbiór osobisty  |
      | InPost Paczkomat |

  @logged_in_order @0056 @order_logged_in_sequential_1
  Scenario: Verify order with saved address as a logged in user with same as delivery checkbox
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    When select one saved shipping address from the list
    And check the same as delivery checkbox
    And click on continue button
    Then verify the shipping and billing addresses are the same
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0057 @order_logged_in_sequential_1 @smoke
  Scenario: Verify order with saved address as a logged in user with different billing address
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "company"
    And click on continue button
    Then verify the shipping and billing addresses are different
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0058 @order_logged_in_sequential_1 @smoke
  Scenario: Verify user can change the saved shipping address to a different one and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And click on continue button
    And click on change order details button
    And select one saved shipping address from the list
    And click on continue button
    And verify the newly selected shipping address is different from the previous one
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0059 @order_logged_in_sequential_1
  Scenario: Verify user can change the saved billing address to a different one and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "company"
    And click on continue button
    And click on change order details button
    And select one saved billing address from the list as a "company"
    And click on continue button
    And verify the newly selected billing address is different from the previous one
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @logged_in_order @0060 @order_logged_in_sequential_1
  Scenario: Verify user can change the saved shipping and billing addresses to different ones and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "company"
    And click on continue button
    And click on change order details button
    And select one saved shipping address from the list
    And select one saved billing address from the list as a "company"
    And click on continue button
    And verify the newly selected shipping and billing addresses are different from the previous ones
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed


  @0061 @order_logged_in_sequential_1
  Scenario: Verify user can change the saved billing address to a different one as a company and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "company"
    And click on continue button
    And click on change order details button
    And select one saved billing address from the list as a "company"
    And click on continue button
    And verify the shipping and billing addresses are different
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0062 @order_logged_in_sequential_1
  Scenario: Verify user can change the saved billing address to a different one as a person and order successfully
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And select one saved billing address from the list as a "person"
    And click on continue button
    And click on change order details button
    And select one saved billing address from the list as a "person"
    And click on continue button
    And verify the shipping and billing addresses are different
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0063 @order_logged_in_sequential_1
  Scenario: Verify order with new shipping address as a logged in user with same as delivery checkbox
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And click on add new shipping address button
    And user adds new shipping address as "person"
    And check the same as delivery checkbox
    And click on continue button
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    Then verify the shipping and billing addresses are the same
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0064 @order_logged_in_sequential_1
  Scenario: Verify order with new billing address as a person and logged in user
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And select one saved shipping address from the list
    And uncheck the same as delivery checkbox
    And click on add new billing address button
    And user adds new billing address as "person"
    And click on continue button
    And verify the shipping and billing addresses are different
    And user selects delivery method "Odbiór osobisty"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed




