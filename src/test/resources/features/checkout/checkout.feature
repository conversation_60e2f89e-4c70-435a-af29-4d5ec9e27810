@regression @checkout
Feature: Checkout Tests

  Background: Open the home page
    Given that user is on the home page

  @0187 @smoke
  Scenario: Verify user can login at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user clicks on login link at checkout
    And user logs in at checkout with account test credentials
    Then verify user is logged in at checkout
    And verify saved shipping addresses are listed
    And verify saved billing addresses are listed
    And select one saved shipping address from the list
    And check the same as delivery checkbox
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed
    And go to my account page
    When navigate to order history
    Then verify the recent order is displayed in order history
    And verify order details match the placed order


  @0196
  Scenario: Verify checkout with free shipping threshold
    When user searches for product by code "46223"
    And user adds the product to cart
    And user opens cart
    Then verify free delivery message is displayed in cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And verify the delivery cost is zero at checkout
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

    # ========== SHIPPING ADDRESS VALIDATION TESTS ==========

  @0197
  Scenario: Verify shipping form validation - empty email field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "email" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0198
  Scenario: Verify shipping form validation - empty first name field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "first name" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0199
  Scenario: Verify shipping form validation - empty last name field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "last name" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0200
  Scenario: Verify shipping form validation - empty street address field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "street address" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0201
  Scenario: Verify shipping form validation - empty city field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "city" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0202
  Scenario: Verify shipping form validation - empty postal code field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "postal code" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0203
  Scenario: Verify shipping form validation - empty phone number field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with empty "phone number" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0204
  Scenario: Verify shipping form validation - invalid phone number
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with invalid "phone number"
    And try to click continue button for validation
    Then verify invalid phone number error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0205
  Scenario: Verify shipping form validation - invalid postal code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills shipping form with invalid "postal code"
    And try to click continue button for validation
    Then verify invalid postal code error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete shipping form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  # ========== BILLING ADDRESS VALIDATION TESTS ==========

  @0206
  Scenario: Verify billing form validation - empty company name field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And uncheck the same as delivery checkbox
    And user fills billing form with empty "company name" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete billing form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0207
  Scenario: Verify billing form validation - empty street address field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And uncheck the same as delivery checkbox
    And user fills billing form with empty "street address" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete billing form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0208
  Scenario: Verify billing form validation - empty city field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And uncheck the same as delivery checkbox
    And user fills billing form with empty "city" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete billing form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  #Comment out for now, because of bug
  #@checkout @0209
  #Scenario: Verify billing form validation - empty NIP field
  #  When user searches for "Fotel"
  #  And user opens the first product
  #  And user adds the product to cart
  #  And user opens cart
  #  And open cart and proceed to checkout
  #  And user fills recipient information as "individual"
  #  And uncheck the same as delivery checkbox
  #  And user fills billing form with empty "NIP" field
  #  And try to click continue button for validation
  #  Then verify empty required field error message is displayed
  #  And verify error message before continue button is displayed
  #  And verify continue button is disabled
  #  When user fills complete billing form with valid data
  #  Then verify continue button is enabled
  #  And click on continue button
  #  And user selects delivery method "Kurier"
  #  And click on continue button
  #  And user agrees to terms and conditions
  #  And user chooses to pay with "Blik"
  #  Then verify order confirmation page is displayed

  @0210
  Scenario: Verify billing form validation - empty postal code field
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And uncheck the same as delivery checkbox
    And user fills billing form with empty "postal code" field
    And try to click continue button for validation
    Then verify empty required field error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete billing form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0211
  Scenario: Verify billing form validation - invalid postal code
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And uncheck the same as delivery checkbox
    And user fills billing form with invalid "postal code"
    And try to click continue button for validation
    Then verify invalid postal code error message is displayed
    And verify error message before continue button is displayed
    And verify continue button is disabled
    When user fills complete billing form with valid data
    Then verify continue button is enabled
    #veify user can order
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0312
  Scenario: Verify registration at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user selects register at checkout checkbox
    And user fills registration information at checkout
    And user fills recipient information as "person"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0313
  Scenario Outline: Verify password validation at checkout registration - invalid password lengths
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills registration form with invalid password "<password>"
    And try to click continue button for validation
    Then verify invalid or empty password error message is displayed
    And verify minimum password length label is displayed
    Examples:
      | password |
      |          |
      | 1        |
      | 12       |
      | 123      |
      | 1234     |
      | 12345    |
      | 123456   |
      | 1234567  |

  @0314
  Scenario: Verify user can fix invalid password and complete registration at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills registration form with invalid password "123"
    And try to click continue button for validation
    Then verify invalid or empty password error message is displayed
    And verify minimum password length label is displayed
    When user fixes invalid password with valid password
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user chooses to pay with "Blik"
    Then verify order confirmation page is displayed

  @0315
  Scenario: Verify registration option is not available at checkout when user is logged in
    Given user logs in with account test credentials
    And remove product from cart if present
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify register at checkout checkbox is not displayed

  @0316
  Scenario: Verify login option is not available at checkout when user is logged in
    Given user logs in with account test credentials
    And remove product from cart if present
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify login link at checkout is not displayed

  @0317
  Scenario: Verify shipping country selector is disabled at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify shipping country selector is disabled

  @0318
  Scenario: Verify user can navigate to PDP using product link at checkout
    When user searches for "Fotel"
    And user opens the first product
    And save product details to storage
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    When user clicks on product link at checkout
    Then verify user is on PDP from checkout

  @0319
  Scenario: Verify user can open cart using show cart link at checkout
    When user searches for "Fotel"
    And user opens the first product
    And save product details to storage
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    When user clicks on show cart link at checkout
    Then verify user is on cart from checkout

  @checkout @0321
  Scenario: Verify free shipping threshold progress bar and remaining amount at checkout
    When user searches for product by code "47550"
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify free shipping threshold progress bar shows correct percentage at checkout
    And verify remaining amount for free shipping is correct at checkout
    When user clicks on show cart link at checkout
    And user clicks on plus button to increase quantity
    And open cart and proceed to checkout
    Then verify free shipping threshold progress bar shows correct percentage at checkout
    And verify remaining amount for free shipping is correct at checkout
    When user clicks on show cart link at checkout
    And user changes quantity to random amount in cart
    And open cart and proceed to checkout
    Then verify free shipping threshold progress bar shows correct percentage at checkout
    And verify remaining amount for free shipping is correct at checkout

  @checkout @0322
  Scenario: Verify free shipping threshold and remaining amount at every step of checkout
    When user searches for product by code "47550"
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify free shipping threshold progress bar shows correct percentage at checkout
    And verify remaining amount for free shipping is correct at checkout
    When user fills recipient information as "individual"
    And click on continue button
    Then verify free shipping threshold progress bar shows correct percentage at checkout
    And verify remaining amount for free shipping is correct at checkout

  @checkout @0323
  Scenario: Verify cart details are displayed at every step of checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    Then verify cart details are displayed correctly at checkout
    When user fills recipient information as "individual"
    And click on continue button
    Then verify cart details are displayed correctly at checkout
    When user selects delivery method "Kurier"
    And click on continue button
    Then verify cart details are displayed correctly at checkout
    When user agrees to terms and conditions
    Then verify cart details are displayed correctly at checkout

  @0326
  Scenario: Verify all payment icons are displayed at checkout
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    Then verify all payment icons are displayed at checkout

  @0327
  Scenario: Verify user can select all payment methods
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    Then verify user can select all payment methods

  @0329
  Scenario: Verify error message is displayed when user cancels Apple Pay payment
    When user searches for "Fotel"
    And user opens the first product
    And user adds the product to cart
    And user opens cart
    And open cart and proceed to checkout
    And user fills recipient information as "individual"
    And click on continue button
    And user selects delivery method "Kurier"
    And click on continue button
    And user agrees to terms and conditions
    And user selects Apple Pay payment method
    And user clicks on Apple Pay button
    And user cancels the Apple Pay payment window
    Then verify payment cancelled error message is displayed
