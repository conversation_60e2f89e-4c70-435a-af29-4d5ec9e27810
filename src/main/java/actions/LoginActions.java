package actions;

import common.constants.Constants;
import common.test_data.TestDataFactory;
import common.test_data.UserCredentials;
import net.serenitybdd.annotations.Step;
import net.serenitybdd.annotations.Steps;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import pages.BasePage;
import pages.LoginPage;

import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;

public class LoginActions extends UIInteractionSteps {
    @Steps
    private LoginPage loginPage;
    @Steps
    private BasePage basePage;

    @Step
    public void loginWithValidCredentials() {
        UserCredentials credentials = TestDataFactory.getDefaultUserCredentials();
        loginWithCredentials(credentials);
    }

    @Step
    public void loginWithCredentials(UserCredentials credentials) {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(credentials.getEmail());
        loginPage.getPasswordInput().sendKeys(credentials.getPassword());
        loginPage.getLoginButton().click();
        waitABit(2000);
    }

    @Step
    public void loginWithSpecificUser(String userType) {
        UserCredentials credentials = TestDataFactory.getUserCredentials(userType);
        loginWithCredentials(credentials);
    }

    @Step
    public void navigateToLoginPage() {
        loginPage.getLoginLink().click();
        loginPage.getEmailInput().waitUntilVisible();
    }

    @Step
    public void verifyLoginSuccessful() {
        loginPage.getUserAccountIcon().waitUntilVisible();
    }

    @Step
    public void verifyLoginFailed() {
        loginPage.getLoginErrorMessage().waitUntilVisible();
    }

    @Step
    public void logout() {
        loginPage.myAccountButton().click();
        loginPage.getLogoutLink().waitUntilVisible(Duration.ofSeconds(10));
        loginPage.getLogoutLink().click();
        waitABit(3000);
    }

    public void loginWithLoggedInOrderCredentials() {
        loginWithCredentials(UserCredentials.loggedInOrderCredentials());
    }

    @Step
    public void loginWithLoggedInOrderCredentials2() {
        loginWithCredentials(UserCredentials.loggedInOrderCredentials2());
    }

    @Step
    public void loginWithAccountTestCredentials() {
        loginWithCredentials(UserCredentials.accountTestCredentials());
    }

    @Step
    public void enterValidCredentials() {
        UserCredentials credentials = TestDataFactory.getDefaultUserCredentials();
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(credentials.getEmail());
        loginPage.getPasswordInput().sendKeys(credentials.getPassword());
    }

    @Step
    public void enterInvalidEmailAndValidPassword(String invalidEmail) {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(invalidEmail);
        loginPage.getPasswordInput().sendKeys(UserCredentials.defaultCredentials().getPassword());
    }

    @Step
    public void enterValidEmailAndEmptyPassword() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(UserCredentials.defaultCredentials().getEmail());
        loginPage.getPasswordInput().sendKeys("");
    }

    @Step
    public void enterInvalidCredentials() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().clearText();
        loginPage.getPasswordInput().clearText();
        loginPage.getEmailInput().sendKeys("<EMAIL>");
        loginPage.getPasswordInput().sendKeys("wrongpassword");
    }

    @Step
    public void clickLoginButton() {
        loginPage.loginButton().click();
        waitABit(2000);
    }

    @Step
    public void verifyInvalidEmailErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.invalidEmailErrorMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.invalidEmailErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.INVALID_EMAIL);
        softAssertions.assertAll();
    }

    @Step
    public void verifyEmptyPasswordErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.emptyPasswordErrorMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.emptyPasswordErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.EMPTY_PASSWORD_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void verifyInvalidLoginCredentialsErrorMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.invalidLoginErrorMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.invalidLoginErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.INVALID_LOGIN_CREDENTIALS);
        softAssertions.assertAll();
    }

    @Step
    public void verifyLoginModalHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.loginModalHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.loginModalHeader().getText()).isEqualTo(Constants.LoginModal.LOGIN_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyLoginModalSubHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.loginModalSubHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.loginModalSubHeader().getText()).isEqualTo(Constants.LoginModal.LOGIN_SUB_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyForgotPasswordLinkDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.getForgotPasswordLink().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(loginPage.getForgotPasswordLink().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyRegisterLinkDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registerLink().waitUntilPresent(Duration.ofSeconds(10));
        softAssertions.assertThat(loginPage.registerLink().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyYouDontHaveAccountTextDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.youDontHaveAnAccountYetText().waitUntilVisible();
        softAssertions.assertThat(loginPage.youDontHaveAnAccountYetText().getText()).isEqualTo(Constants.LoginModal.YOU_DONT_HAVE_ACCOUNT_TEXT);
        softAssertions.assertAll();
    }

    @Step
    public void clickForgotPasswordLink() {
        loginPage.getForgotPasswordLink().click();
        waitABit(1000);
    }

    @Step
    public void verifyForgotPasswordModalHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.forgotPasswordModalHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.forgotPasswordModalHeader().getText()).isEqualTo(Constants.LoginModal.FORGOT_PASSWORD_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyForgotPasswordDescription() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.forgotPasswordDescription().waitUntilVisible();
        softAssertions.assertThat(loginPage.forgotPasswordDescription().getText()).isEqualTo(Constants.LoginModal.FORGOT_PASSWORD_DESCRIPTION);
        softAssertions.assertAll();
    }

    @Step
    public void enterValidEmailForPasswordReset() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().clearText();
        loginPage.getEmailInput().sendKeys(UserCredentials.defaultCredentials().getEmail());
    }

    @Step
    public void enterInvalidEmailForPasswordReset(String invalidEmail) {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().clearText();
        loginPage.getEmailInput().sendKeys(invalidEmail);
    }

    @Step
    public void clearEmailFieldForPasswordReset() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().clearText();
    }

    @Step
    public void clickResetPasswordButton() {
        loginPage.resetPasswordButton().click();
        waitABit(2000);
    }

    @Step
    public void verifyResetPasswordSuccessMessageHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.resetPasswordSuccessMessageHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.resetPasswordSuccessMessageHeader().getText()).isEqualTo(Constants.LoginModal.RESET_PASSWORD_SUCCESS_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyResetPasswordSuccessMessage() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.resetPasswordSuccessMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.resetPasswordSuccessMessage().getText()).isEqualTo(Constants.LoginModal.RESET_PASSWORD_SUCCESS_MESSAGE);
        softAssertions.assertAll();
    }

    @Step
    public void clickCloseResetPasswordModalButton() {
        loginPage.closeResetPasswordModalButton().click();
        waitABit(1000);
    }

    @Step
    public void verifyLoginModalIsClosed() {
        loginPage.loginModalHeader().waitUntilNotVisible();
        assertThat(loginPage.loginModalHeader().isVisible()).isFalse();
    }

    @Step
    public void clickReturnToLoginButton() {
        loginPage.returnToLoginButtonOnResetPasswordModal().click();
        waitABit(1000);
    }

    @Step
    public void verifyInvalidEmailErrorOnForgotPasswordModal() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.invalidEmailErrorMessage().waitUntilVisible();
        softAssertions.assertThat(loginPage.invalidEmailErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.INVALID_EMAIL);
        softAssertions.assertAll();
    }

    @Step
    public void closeLoginModal() {
        loginPage.getCloseModalButton().click();
        waitABit(1000);
    }

    @Step
    public void clickLoginLinkAgain() {
        loginPage.getLoginLink().click();
        waitABit(1000);
    }

    @Step
    public void clickRegisterLink() {
        loginPage.registerLink().click();
        waitABit(1000);
    }

    @Step
    public void verifyRegisterModalHeader() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.registerModalHeader().waitUntilVisible();
        softAssertions.assertThat(loginPage.registerModalHeader().getText()).isEqualTo(Constants.LoginModal.REGISTER_HEADER);
        softAssertions.assertAll();
    }

    @Step
    public void verifyLoginLinkDisplayedOnRegisterModal() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.loginLinkOnRegisterModal().waitUntilVisible();
        softAssertions.assertThat(loginPage.loginLinkOnRegisterModal().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void clickLoginLinkOnRegisterModal() {
        loginPage.loginLinkOnRegisterModal().click();
        waitABit(1000);
    }

    @Step
    public void enterWhitespaceEmailAndPassword() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(Constants.TestData.WHITESPACE_EMAIL);
        loginPage.getPasswordInput().sendKeys(Constants.TestData.WHITESPACE_PASSWORD);
    }

    @Step
    public void verifyAppropriateValidationErrorMessages() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.invalidEmailErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.invalidEmailErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.INVALID_EMAIL);
        softAssertions.assertThat(loginPage.emptyPasswordErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.EMPTY_PASSWORD_ERROR);
        softAssertions.assertAll();
    }

    @Step
    public void enterVeryLongEmailAndPassword() {
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().sendKeys(Constants.TestData.VERY_LONG_EMAIL);
        loginPage.getPasswordInput().sendKeys(Constants.TestData.VERY_LONG_PASSWORD);
    }

    @Step
    public void verifyLoginAttemptHandledAppropriately() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.invalidLoginErrorMessage().waitUntilVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(loginPage.invalidLoginErrorMessage().getText()).isEqualTo(Constants.ValidationMessages.INVALID_LOGIN_CREDENTIALS);
        softAssertions.assertAll();
    }

    @Step
    public void refreshPage() {
        getDriver().navigate().refresh();
        waitABit(3000);
    }

    @Step
    public void verifyUserRemainsLoggedIn() {
        SoftAssertions softAssertions = new SoftAssertions();
        basePage.myAccountButton().waitUntilVisible(Duration.ofSeconds(10));
        softAssertions.assertThat(basePage.myAccountButton().isVisible()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void enterInvalidCredentialsAgain() {
        enterInvalidCredentials();
    }

    @Step
    public void navigateThroughLoginFormUsingTabKey() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.getEmailInput().waitUntilVisible();
        loginPage.getEmailInput().click();
        softAssertions.assertThat(loginPage.getEmailInput().isEnabled()).isTrue();
        getDriver().findElement(By.xpath("//input[@name='email']")).sendKeys(Keys.TAB);
        waitABit(500);
        softAssertions.assertThat(loginPage.getPasswordInput().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyAllFormElementsAccessibleViaKeyboard() {
        SoftAssertions softAssertions = new SoftAssertions();
        softAssertions.assertThat(loginPage.getEmailInput().isEnabled()).isTrue();
        softAssertions.assertThat(loginPage.getPasswordInput().isEnabled()).isTrue();
        softAssertions.assertThat(loginPage.loginButton().isEnabled()).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void verifyUserCanSubmitFormUsingEnterKey() {
        loginPage.getEmailInput().sendKeys(UserCredentials.defaultCredentials().getEmail());
        loginPage.getPasswordInput().sendKeys(UserCredentials.defaultCredentials().getPassword());
        getDriver().findElement(By.xpath("//input[@name='password']")).sendKeys(Keys.ENTER);
        waitABit(2000);
    }

    @Step
    public void closeForgotPasswordModal() {
        loginPage.getCloseModalButton().click();
        waitABit(1000);
    }

    @Step
    public void verifyForgotPasswordModalIsClosed() {
        SoftAssertions softAssertions = new SoftAssertions();
        loginPage.forgotPasswordModalHeader().waitUntilNotVisible(Duration.ofSeconds(5));
        softAssertions.assertThat(true).isTrue();
        softAssertions.assertAll();
    }

    @Step
    public void loginWithAccountTestCredentials2() {
        loginWithCredentials(UserCredentials.accountTestCredentials2());
    }

}