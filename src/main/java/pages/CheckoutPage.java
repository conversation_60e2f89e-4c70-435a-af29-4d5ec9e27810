package pages;

import net.serenitybdd.core.pages.PageObject;
import objects_behaviors.implementation.*;
import objects_behaviors.rules.*;
import org.openqa.selenium.By;

public class CheckoutPage extends PageObject {
    private static final String
            RADIO_BUTTONS = "//p[contains(@class,'body-m_bold') and normalize-space(text())='%s']//preceding-sibling::input[@type='radio']",
            ONLINE_BANK_PAYMENT_OPTION = "//li[contains(@class, 'adyen-checkout__dropdown__element')]//span[text()='%s']",
            DELIVERY_COST_ON_DELIVERY_METHOD_SELECTION = "//p[contains(@class,'body-m_bold') and normalize-space(text())='%s']//preceding-sibling::input[@type='radio']//..//..//div[contains(@class, 'ml-auto flex flex-row')]/div",
            BILLING_COUNTRY_SELECT_BY_NAME = "//div[@id=\"select-options\"]//li//p[contains(text(), '%s')]";
    private final By
            EMAIL_INPUT = By.xpath("//input[@autocomplete='email']"),
            FIRST_NAME_INPUT = By.xpath("//input[@autocomplete='shipping given-name']"),
            LAST_NAME_INPUT = By.xpath("//input[@autocomplete='shipping family-name']"),
            PHONE_INPUT = By.xpath("//input[@autocomplete='tel']"),
            ADDRESS_INPUT = By.xpath("//input[@autocomplete='shipping address-line1']"),
            POSTAL_CODE_INPUT = By.xpath("//input[@autocomplete='shipping postal-code']"),
            CITY_INPUT = By.xpath("//input[@autocomplete='shipping address-level2']"),
            COMPANY_NAME_INPUT = By.xpath("//input[@autocomplete='shipping off']"),
            SAME_AS_DELIVERY_CHECKBOX = By.xpath("//div[@data-sentry-component=\"BillingSameCheckbox\"]//input"),
            INVOICE_FOR_COMPANY_OPTION = By.xpath("(//div[contains(@class, 'flex') and .//div[text()='Firma']])[3]"),
            COMPANY_BILLING_NAME_INPUT = By.xpath("//input[@autocomplete='billing off']"),
            NIP_BILLING_INPUT = By.xpath("//span[contains(text(), 'NIP')]//..//..//..//div//input"),
            AS_COMPANY_STREET_INPUT = By.xpath("//input[@autocomplete='billing address-line1']"),
            AS_COMPANY_CITY_INPUT = By.xpath("//input[@autocomplete='billing address-level2']"),
            AS_COMPANY_POST_CODE_INPUT = By.xpath("//input[@autocomplete='billing postal-code']"),
            AS_COMPANY_PHONE_INPUT = By.xpath("//span[contains(text(), 'Numer telefonu (opcjonalne)')]//..//..//input"),
            INVOICE_FOR_PRIVATE_OPTION = By.xpath("(//div[contains(@class, 'flex') and .//div[text()='Osoba fizyczna']])[3]"),
            AS_PERSON_GIVEN_NAME_INPUT = By.xpath("//input[@autocomplete='billing given-name']"),
            AS_PERSON_LAST_NAME_INPUT = By.xpath("//input[@autocomplete='billing family-name']"),
            AS_PERSON_PHONE_INPUT = By.xpath("//span[contains(text(), 'Numer telefonu (opcjonalne)')]//..//..//input"),
            AS_PERSON_ADDRESS_INPUT = By.xpath("//input[@autocomplete='billing address-line1']"),
            AS_PERSON_POSTAL_CODE_INPUT = By.xpath("//input[@autocomplete='billing postal-code']"),
            AS_PERSON_CITY_INPUT = By.xpath("//input[@autocomplete='billing address-level2']"),
            DELIVERY_METHOD_PICKUP = By.xpath("//div[@class='flex flex-col gap-2']//div[contains(@class, 'flex') and .//div[text()='Odbiór osobisty']]"),
            PAYMENT_AGREEMENT_WebElement = By.xpath("//p[contains(text(), 'Przeczytałem')]/preceding-sibling::input[@type='checkbox']"),
            BLIK_PAYMENT_INPUT = By.xpath("//input[@name='blikCode']"),
            CARD_NUMBER_INPUT = By.xpath("//input[@data-fieldtype=\"encryptedCardNumber\"]"),
            CARD_EXPIRATION_DATE_INPUT = By.xpath("//input[@data-fieldtype=\"encryptedExpiryDate\"]"),
            CARD_SECURITY_CODE_INPUT = By.xpath("//input[@data-fieldtype=\"encryptedSecurityCode\"]"),
            BLIK_PAY_BUTTON = By.xpath("(//button[.//span[contains(text(), 'Zapłać')]])[1]"),
            CARD_PAY_BUTTON = By.xpath("(//button[.//span[contains(text(), 'Zapłać')]])[2]"),
            ONLINE_TRANSFER_PAY_BUTTON = By.xpath("(//button[.//span[contains(text(), 'Zapłać')]])[3]"),
            ONLINE_TRANSFER_DROPDOWN = By.xpath("//div[contains(@class, 'adyen-checkout__dropdown__button')]//input[contains(@class, 'adyen-checkout__filter-input')]"),
            PRZELEWY24_PAY_BUTTON = By.xpath("//button[contains(@class, 'btn-success') and text()='Zapłać']"),
            CONTINUE_BUTTON = By.xpath("//button//span[contains(text(), 'Dalej')]//..//..//button"),
            CONTINUE_BUTTON_FOR_DELIVERY = By.xpath("//div[contains(@class, 'block')]//button//span[contains(text(), 'Dalej')]"),
            DELIVERY_PRICE_HEADER = By.xpath("//div[@data-sentry-component=\"ShippingCost\"]"),
            INVALID_BLIK_CODE_MESSAGE = By.xpath("//div[contains(text(), 'Nieprawidłowy kod BLIK')]"),
            INVALID_CARD_NUMBER_MESSAGE = By.xpath("//div[contains(@class, \"text-error\")]"),
            CHANGE_ORDER_DETAILS_BUTTON = By.xpath("//a[contains(text(), \"Zmień\")]//..//h3//../a"),
            SAVED_SHIPPING_ADDRESS_LIST = By.xpath("(//h3[contains(text(), 'Dane odbiorcy')]//..//..//./div/div[1])[1]//div[@data-sentry-component=\"AddressSummaryFormatting\"]"),
            SAVED_BILLING_ADDRESS_LIST = By.xpath("(//div[contains(@class, 'lg:grid-cols-2')])[2]//div[@data-sentry-component=\"AddressSummaryFormatting\"]"),
            SELECTED_SAVED_ADDRESS_CHECK_MARK = By.xpath("(//h3[contains(text(), 'Dane odbiorcy')]//..//..//./div/div[1])[1]//div[@data-sentry-component='AddressSummaryFormatting']//..//div//*[local-name()='svg']"),
            SELECTED_SHIPPING_ADDRESS = By.xpath("(//div[@data-sentry-component=\"AddressSummaryFormatting\"])[1]"),
            SELECTED_BILLING_ADDRESS = By.xpath("(//div[@data-sentry-component=\"AddressSummaryFormatting\"])[2]"),
            ADD_NEW_SHIPPING_ADDRESS_BUTTON = By.xpath("(//h3[contains(text(), 'Dane odbiorcy')]//..)[1]/div[2]//button//span[contains(text(), 'Dodaj nowy adres')]"),
            ADD_NEW_BILLING_ADDRESS_BUTTON = By.xpath("(//h3[contains(text(), 'Dane do faktury')]//..)[1]/div[4]/div[3]//button//span[contains(text(), 'Dodaj nowy adres')]"),
            BILLING_COUNTRY_SELECTOR = By.xpath("//button[@aria-controls=\"select-options\"]"),
            BILLING_COUNTRY_OPTION = By.xpath("//div[@id=\"select-options\"]//li//p[contains(text(), '')]"),
            CHANGE_DELIVERY_METHOD_BUTTON = By.xpath("(//a[@href=\"/pl/PL/checkout/shipping\"])[1]"),
            LOGIN_LINK_AT_CHECKOUT = By.xpath("//div[@data-sentry-component=\"CheckoutSection\"]/div[2]/div/div/div//span[contains(@class, \"cursor-pointer\")]"),
            LOGIN_EMAIL_INPUT = By.xpath("//input[@name='email']"),
            LOGIN_PASSWORD_INPUT = By.xpath("//input[@name='password']"),
            LOGIN_BUTTON = By.xpath("//button//span[contains(text(), \"Zaloguj się\")]"),
            CHECKOUT_SUBTOTAL = By.xpath("//div[contains(text(), 'Wartość produktów')]/following-sibling::div"),
            CHECKOUT_DISCOUNT_HEADER = By.xpath("//div[contains(text(), 'Rabat')]"),
            CHECKOUT_DISCOUNT_VALUE = By.xpath("//div[contains(text(), 'Rabat')]/following-sibling::div"),
            CHECKOUT_TOTAL_PRICE = By.xpath("//div[contains(text(), 'Razem')]/following-sibling::div"),
            CHECKOUT_APPLIED_COUPON_CODE = By.xpath("//div[contains(text(), 'Kod rabatowy')]/following-sibling::div"),
            CHECKOUT_VAT_AMOUNT = By.xpath("//div[contains(text(), 'Podatek VAT')]/following-sibling::div"),
            ERROR_MESSAGE_FOR_EMPTY_REQUIRED_FIELD = By.xpath("//div[contains(text(), 'To pole jest wymagane')]"),
            ERROR_MESSAGE_BEFORE_CONTINUE_BUTTON = By.xpath("//p[contains (@class,'text-error')]"),
            ERROR_MESSAGE_FOR_INVALID_POSTAL_CODE = By.xpath("//div[contains(text(), 'Nieprawidłowy kod pocztowy')]"),
            ERROR_MESSAGE_FOR_INVALID_PHONE_NUMBER = By.xpath("//div[contains(text(), 'Nieprawidłowy numer telefonu')]"),
            ERROR_MESSAGE_FOR_INVALID_EMAIL = By.xpath("//div[contains(text(), 'Podaj poprawny adres email')]"),
            REGISTER_AT_CHECKOUT_CHECKBOX = By.xpath("//input[@type=\"checkbox\"]//..//..//div[3]//p[contains(text(), 'Chcę założyć konto klienta')]/../input"),
            PASSWORD_INPUT = By.xpath("//input[@name='password']"),
            BUY_WITHOUT_LOGIN = By.xpath("//span[contains(normalize-space(), 'Kup bez logowania')]"),
            INVALID_OR_EMPTY_PASSWORD_ERROR_MESSAGE = By.xpath("//p[contains(text(), 'Hasło jest za krótkie')]"),
            MINIMUM_PASSWORD_LENGTH_LABEL = By.xpath("//label[contains(text(), 'Ustaw hasło (minimum 8 znaków)')]"),
            SHIPPING_COUNTRY_SELECTOR = By.xpath("//input[contains(@class, 'text-disabled') and @autocomplete=\"off\"]"),
            PRODUCT_LINK_AT_CHECKOUT = By.xpath("//div[contains(text(), 'Twoje zamówienie (1)')]//..//..//div[2]//a"),
            SHOW_CART_LINK_AT_CHECKOUT = By.xpath("//div[contains(text(), 'Twoje zamówienie (1)')]//..//..//div[1]//a[contains(text(), 'Zobacz')]"),
            APPLE_PAY_LOGO = By.xpath("//img[@alt='Apple Pay']"),
            GOOGLE_PAY_LOGO = By.xpath("//img[@alt='Google Pay']"),
            BLIK_LOGO = By.xpath("//img[@alt=\"BLIK z kodem\"]"),
            CARD_LOGO = By.xpath("//img[@alt='Karta bankowa']"),
            ONLINE_TRANSFER_LOGO = By.xpath("//img[@alt='Przelewy online']"),
            APPLE_PAY_BUTTON = By.xpath("//div[@data-sentry-component=\"CheckoutRadio\"]//div[contains(@class, 'block')]/div//apple-pay-button"),
            GOOGLE_PAY_BUTTON = By.xpath("//div[@data-testid=\"googlepay-button-container\"]//button"),
            PAYMENT_CANCELLED_ERROR_MESSAGE = By.xpath("//div[contains(text(), 'Płatność zaostała anulowana. Spróbuj ponownie lub skorzystaj z innej metody płatności.')]");


    public IRadioButton getRadioButtonsOnCheckoutPage(String name) {
        var element = String.format(RADIO_BUTTONS, name);
        return new RadioButton($(element), String.format("%s Radio Button", name));
    }

    public IRadioButton getOnlineBankPaymentOption(String name) {
        var element = String.format(ONLINE_BANK_PAYMENT_OPTION, name);
        return new RadioButton($(element), String.format("%s Online Bank Payment Option", name));
    }

    public IRadioButton getDeliveryMethodPickup() {
        return new RadioButton($(DELIVERY_METHOD_PICKUP), "Delivery Method Pickup");
    }

    public IInputField getEmailInput() {
        return new InputField($(EMAIL_INPUT), "Email Input");
    }

    public IInputField getFirstNameInput() {
        return new InputField($(FIRST_NAME_INPUT), "First Name Input");
    }

    public IInputField getLastNameInput() {
        return new InputField($(LAST_NAME_INPUT), "Last Name Input");
    }

    public IInputField getPhoneInput() {
        return new InputField($(PHONE_INPUT), "Phone Input");
    }

    public IInputField getAddressInput() {
        return new InputField($(ADDRESS_INPUT), "Address Input");
    }

    public IInputField getPostalCodeInput() {
        return new InputField($(POSTAL_CODE_INPUT), "Postal Code Input");
    }

    public IInputField getCityInput() {
        return new InputField($(CITY_INPUT), "City Input");
    }

    public IInputField getCompanyNameInput() {
        return new InputField($(COMPANY_NAME_INPUT), "Company Name Input");
    }

    public IWebElement getInvoiceWebElement() {
        return new WebElement($(SAME_AS_DELIVERY_CHECKBOX), "Invoice WebElement");
    }

    public IWebElement getInvoiceForCompanyOption() {
        return new WebElement($(INVOICE_FOR_COMPANY_OPTION), "Invoice For Company Option");
    }

    public IWebElement getInvoiceForPrivateOption() {
        return new WebElement($(INVOICE_FOR_PRIVATE_OPTION), "Invoice For Private Option");
    }

    public IInputField getCompanyBillingNameInput() {
        return new InputField($(COMPANY_BILLING_NAME_INPUT), "Company Billing Name Input");
    }

    public IInputField getNipBillingInput() {
        return new InputField($(NIP_BILLING_INPUT), "NIP Billing Input");
    }

    public IInputField getAsCompanyStreetInput() {
        return new InputField($(AS_COMPANY_STREET_INPUT), "As Company Street Input");
    }

    public IInputField getAsCompanyCityInput() {
        return new InputField($(AS_COMPANY_CITY_INPUT), "As Company City Input");
    }

    public IInputField getAsCompanyPostCodeInput() {
        return new InputField($(AS_COMPANY_POST_CODE_INPUT), "As Company Post Code Input");
    }

    public IInputField getAsPersonGivenNameInput() {
        return new InputField($(AS_PERSON_GIVEN_NAME_INPUT), "As Person Given Name Input");
    }

    public IInputField getAsPersonLastNameInput() {
        return new InputField($(AS_PERSON_LAST_NAME_INPUT), "As Person Last Name Input");
    }

    public IInputField getAsPersonPhoneInput() {
        return new InputField($(AS_PERSON_PHONE_INPUT), "As Person Phone Input");
    }

    public IInputField getAsPersonAddressInput() {
        return new InputField($(AS_PERSON_ADDRESS_INPUT), "As Person Address Input");
    }

    public IInputField getAsPersonPostalCodeInput() {
        return new InputField($(AS_PERSON_POSTAL_CODE_INPUT), "As Person Postal Code Input");
    }

    public IInputField getAsPersonCityInput() {
        return new InputField($(AS_PERSON_CITY_INPUT), "As Person City Input");
    }

    public IWebElement getPaymentAgreementCheckbox() {
        return new WebElement($(PAYMENT_AGREEMENT_WebElement), "Payment Agreement WebElement");
    }

    public IWebElement invoiceForCompanyOption() {
        return new WebElement($(INVOICE_FOR_COMPANY_OPTION), "Invoice For Company Option");
    }

    public IWebElement invoiceForPrivateOption() {
        return new WebElement($(INVOICE_FOR_PRIVATE_OPTION), "Invoice For Private Option");
    }


    public IInputField getBlikPaymentInput() {
        return new InputField($(BLIK_PAYMENT_INPUT), "Blik Payment Input");
    }

    public IInputField getCardNumberInput() {
        return new InputField($(CARD_NUMBER_INPUT), "Card Number Input");
    }

    public IInputField getCardExpirationDateInput() {
        return new InputField($(CARD_EXPIRATION_DATE_INPUT), "Card Expiration Date Input");
    }

    public IInputField getCardSecurityCodeInput() {
        return new InputField($(CARD_SECURITY_CODE_INPUT), "Card Security Code Input");
    }

    public IWebElement getPayButton() {
        return new WebElement($(BLIK_PAY_BUTTON), "Pay Button");
    }

    public IWebElement getOnlineTransferDropdown() {
        return new WebElement($(ONLINE_TRANSFER_DROPDOWN), "Online Transfer Dropdown");
    }

    public IWebElement getPrzelewy24PayButton() {
        return new WebElement($(PRZELEWY24_PAY_BUTTON), "Przelewy24 Pay Button");
    }

    public ICheckBox sameAsDeliveryCheckbox() {
        return new CheckBox($(SAME_AS_DELIVERY_CHECKBOX), "Same As Delivery Checkbox");
    }

    public IInputField companyPhoneInput() {
        return new InputField($(AS_COMPANY_PHONE_INPUT), "Company Phone Input");
    }

    public IInputField getAsCompanyPhoneInput() {
        return new InputField($(AS_COMPANY_PHONE_INPUT), "As Company Phone Input");
    }

    public IWebElement getOnlineTransferPayButton() {
        return new WebElement($(ONLINE_TRANSFER_PAY_BUTTON), "Online Transfer Pay Button");
    }

    public IWebElement getCardPayButton() {
        return new WebElement($(CARD_PAY_BUTTON), "Card Pay Button");
    }

    public IWebElement getBlikPayButton() {
        return new WebElement($(BLIK_PAY_BUTTON), "Blik Pay Button");
    }

    public IWebElement getContinueButton() {
        return new WebElement($(CONTINUE_BUTTON), "Continue Button");
    }

    public IWebElement getContinueButtonForDelivery() {
        return new WebElement($(CONTINUE_BUTTON_FOR_DELIVERY), "Continue Button For Delivery");
    }

    public IWebElement getDeliveryPriceHeader() {
        return new WebElement($(DELIVERY_PRICE_HEADER), "Delivery Price Header");
    }

    public IWebElement deliveryCostOnDeliveryMethodSelection(String deliveryMethod) {
        return new WebElement($(String.format(DELIVERY_COST_ON_DELIVERY_METHOD_SELECTION, deliveryMethod)), "Delivery Cost on Delivery Method Selection");
    }

    public IWebElement getInvalidBlikCodeMessage() {
        return new WebElement($(INVALID_BLIK_CODE_MESSAGE), "Invalid BLIK Code Message");
    }

    public IWebElement getInvalidCardNumberMessage() {
        return new WebElement($(INVALID_CARD_NUMBER_MESSAGE), "Invalid Card Number Message");
    }

    public IWebElement getChangeOrderDetailsButton() {
        return new WebElement($(CHANGE_ORDER_DETAILS_BUTTON), "Change Order Details Button");
    }

    public IWebList getSavedShippingAddressList() {
        return new WebList($$(SAVED_SHIPPING_ADDRESS_LIST), "Saved Shipping Address List");
    }

    public IWebList getSavedBillingAddressList() {
        return new WebList($$(SAVED_BILLING_ADDRESS_LIST), "Saved Billing Address List");
    }

    public IWebElement getSelectedSavedAddressCheckMark() {
        return new WebElement($(SELECTED_SAVED_ADDRESS_CHECK_MARK), "Selected Saved Address Check Mark");
    }

    public IWebElement getSelectedShippingAddress() {
        return new WebElement($(SELECTED_SHIPPING_ADDRESS), "Selected Shipping Address");
    }

    public IWebElement getSelectedBillingAddress() {
        return new WebElement($(SELECTED_BILLING_ADDRESS), "Selected Billing Address");
    }

    public IWebElement addNewShippingAddressButton() {
        return new WebElement($(ADD_NEW_SHIPPING_ADDRESS_BUTTON), "Add New Shipping Address Button");
    }

    public IWebElement addNewBillingAddressButton() {
        return new WebElement($(ADD_NEW_BILLING_ADDRESS_BUTTON), "Add New Billing Address Button");
    }

    public IWebElement getBillingCountrySelector() {
        return new WebElement($(BILLING_COUNTRY_SELECTOR), "Billing Country Selector");
    }

    public IWebElement getBillingCountrySelectByName(String countryName) {
        var element = String.format(BILLING_COUNTRY_SELECT_BY_NAME, countryName);
        return new WebElement($(element), String.format("%s Billing Country Select", countryName));
    }

    public IWebList getBillingCountryOptions() {
        return new WebList($$(BILLING_COUNTRY_OPTION), "Billing Country Options");
    }

    public IWebElement changeDeliveryMethodButton() {
        return new WebElement($(CHANGE_DELIVERY_METHOD_BUTTON), "Change Delivery Method Button");
    }

    public IWebElement getLoginLinkAtCheckout() {
        return new WebElement($(LOGIN_LINK_AT_CHECKOUT), "Login Link At Checkout");
    }

    public IInputField getLoginEmailInput() {
        return new InputField($(LOGIN_EMAIL_INPUT), "Login Email Input");
    }

    public IInputField getLoginPasswordInput() {
        return new InputField($(LOGIN_PASSWORD_INPUT), "Login Password Input");
    }

    public IWebElement getLoginButton() {
        return new WebElement($(LOGIN_BUTTON), "Login Button");
    }

    public IWebElement loginButton() {
        return new WebElement($(LOGIN_BUTTON), "Login Button");
    }

    public IWebElement loginLinkAtCheckout() {
        return new WebElement($(LOGIN_LINK_AT_CHECKOUT), "Login Link At Checkout");
    }

    public IWebElement loginEmailInput() {
        return new WebElement($(LOGIN_EMAIL_INPUT), "Login Email Input");
    }

    public IWebElement loginPasswordInput() {
        return new WebElement($(LOGIN_PASSWORD_INPUT), "Login Password Input");
    }

    public IWebElement getCheckoutSubtotal() {
        return new WebElement($(CHECKOUT_SUBTOTAL), "Checkout Subtotal");
    }

    public IWebElement getCheckoutDiscountHeader() {
        return new WebElement($(CHECKOUT_DISCOUNT_HEADER), "Checkout Discount Header");
    }

    public IWebElement getCheckoutDiscountValue() {
        return new WebElement($(CHECKOUT_DISCOUNT_VALUE), "Checkout Discount Value");
    }

    public IWebElement getCheckoutTotalPrice() {
        return new WebElement($(CHECKOUT_TOTAL_PRICE), "Checkout Total Price");
    }

    public IWebElement getCheckoutAppliedCouponCode() {
        return new WebElement($(CHECKOUT_APPLIED_COUPON_CODE), "Checkout Applied Coupon Code");
    }

    public IWebElement getCheckoutVatAmount() {
        return new WebElement($(CHECKOUT_VAT_AMOUNT), "Checkout VAT Amount");
    }

    public IWebElement errorMessageForEmptyRequiredField() {
        return new WebElement($(ERROR_MESSAGE_FOR_EMPTY_REQUIRED_FIELD), "Error Message For Empty Required Field");
    }

    public IWebElement errorMessageBeforeContinueButton() {
        return new WebElement($(ERROR_MESSAGE_BEFORE_CONTINUE_BUTTON), "Error Message Before Continue Button");
    }

    public IWebElement errorMessageForInvalidPostalCode() {
        return new WebElement($(ERROR_MESSAGE_FOR_INVALID_POSTAL_CODE), "Error Message For Invalid Postal Code");
    }

    public IWebElement errorMessageForInvalidPhoneNumber() {
        return new WebElement($(ERROR_MESSAGE_FOR_INVALID_PHONE_NUMBER), "Error Message For Invalid Phone Number");
    }

    public IWebElement errorMessageForInvalidEmail() {
        return new WebElement($(ERROR_MESSAGE_FOR_INVALID_EMAIL), "Error Message For Invalid Email");
    }

    public ICheckBox registerAtCheckoutCheckbox() {
        return new CheckBox($(REGISTER_AT_CHECKOUT_CHECKBOX), "Register At Checkout Checkbox");
    }

    public IInputField getPasswordInput() {
        return new InputField($(PASSWORD_INPUT), "Password Input");
    }

    public IWebElement buyWithoutLogin() {
        return new WebElement($(BUY_WITHOUT_LOGIN), "Buy Without Login");
    }

    public IWebElement invalidOrEmptyPasswordErrorMessage() {
        return new WebElement($(INVALID_OR_EMPTY_PASSWORD_ERROR_MESSAGE), "Invalid Or Empty Password Error Message");
    }

    public IWebElement minimumPasswordLengthLabel() {
        return new WebElement($(MINIMUM_PASSWORD_LENGTH_LABEL), "Minimum Password Length Label");
    }

    public IWebElement shippingCountrySelector() {
        return new WebElement($(SHIPPING_COUNTRY_SELECTOR), "Shipping Country Selector");
    }

    public IWebElement productLinkAtCheckout() {
        return new WebElement($(PRODUCT_LINK_AT_CHECKOUT), "Product Link At Checkout");
    }

    public IWebElement showCartLinkAtCheckout() {
        return new WebElement($(SHOW_CART_LINK_AT_CHECKOUT), "Show Cart Link At Checkout");
    }

    public IWebElement getApplePayLogo() {
        return new WebElement($(APPLE_PAY_LOGO), "Apple Pay Logo");
    }

    public IWebElement getGooglePayLogo() {
        return new WebElement($(GOOGLE_PAY_LOGO), "Google Pay Logo");
    }

    public IWebElement getBlikLogo() {
        return new WebElement($(BLIK_LOGO), "BLIK Logo");
    }

    public IWebElement getCardLogo() {
        return new WebElement($(CARD_LOGO), "Card Logo");
    }

    public IWebElement getOnlineTransferLogo() {
        return new WebElement($(ONLINE_TRANSFER_LOGO), "Online Transfer Logo");
    }

    public IWebElement applePayButton() {
        return new WebElement($(APPLE_PAY_BUTTON), "Apple Pay Button");
    }

    public IWebElement googlePayButton() {
        return new WebElement($(GOOGLE_PAY_BUTTON), "Google Pay Button");
    }

    public IWebElement paymentCancelledErrorMessage() {
        return new WebElement($(PAYMENT_CANCELLED_ERROR_MESSAGE), "Payment Cancelled Error Message");
    }
}
